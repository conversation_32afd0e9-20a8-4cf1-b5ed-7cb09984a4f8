# Mastra Dictionary Agent

Bu proje, Mastra framework kullanarak MCP (Model Context Protocol) ile entegre edilmiş bir sözlük agent'ı içerir. Agent, OpenAI GPT-4o-mini modelini kullanarak kelimelerin anlamlarını MCP araçları ile bulur ve Türkçe açıklamalar sağlar.

## Özellikler

- 🤖 **Mastra Agent**: OpenAI GPT-4o-mini ile güçlendirilmiş sözlük asistanı
- 🔗 **MCP Entegrasyonu**: Model Context Protocol ile sözlük verilerine erişim
- 💬 **İnteraktif Chat**: Terminal üzerinden kelime sorguları
- 🌐 **Web Playground**: Mastra dev server ile web arayüzü
- 🇹🇷 **Türkçe Destek**: Türkçe açıklamalar ve kullanıcı arayüzü

## Kurulum

1. **Bağımlılıkları yükleyin:**
```bash
npm install
```

2. **Environment değişkenlerini ayarlayın:**
`.env` dosyasında OpenAI API key'inizi ayarlayın:
```
OPENAI_API_KEY=your_openai_api_key_here
```

3. **Projeyi build edin:**
```bash
npm run build:ts
```

## Kullanım

### İnteraktif Chat Modu

Agent ile terminal üzerinden sohbet etmek için:

```bash
npm run agent
```

Bu komut interaktif bir chat başlatır. Kelime yazın ve anlamını öğrenin. Çıkmak için `exit` yazın.

### Test Modu

Agent'ı hızlıca test etmek için:

```bash
npm run agent:test
```

### Web Playground

Mastra dev server'ı başlatmak için:

```bash
npm run dev
```

Server başladıktan sonra şu adreslere erişebilirsiniz:
- **Playground**: http://localhost:4111/
- **API Docs**: http://localhost:4111/openapi.json
- **Swagger UI**: http://localhost:4111/swagger-ui

## Proje Yapısı

```
├── src/
│   └── mastra/
│       ├── agents/
│       │   └── dictionary.ts    # Sözlük agent tanımı
│       └── index.ts             # Mastra instance
├── agent.ts                     # Ana agent uygulaması
├── mastra.config.ts             # Mastra konfigürasyonu
├── .env                         # Environment değişkenleri
└── package.json
```

## Konfigürasyon

### MCP Server

Agent, Smithery platformundaki dictionary MCP server'ını kullanır:
```typescript
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/dictionarymcp/mcp?api_key=YOUR_MCP_KEY"),
    },
  },
});
```

### OpenAI Model

Agent, GPT-4o-mini modelini kullanır:
```typescript
const model = openai('gpt-4o-mini');
```

## Komutlar

- `npm run agent` - İnteraktif chat modunu başlat
- `npm run agent:test` - Agent'ı test et
- `npm run dev` - Mastra dev server'ı başlat
- `npm run build` - Projeyi build et
- `npm run build:ts` - Sadece TypeScript'i derle

## Gereksinimler

- Node.js >= 20.9.0
- OpenAI API Key
- MCP Server erişimi

## Lisans

ISC
