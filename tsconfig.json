{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./", "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node"]}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}