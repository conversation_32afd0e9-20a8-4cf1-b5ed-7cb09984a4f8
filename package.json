{"name": "my-mastra-app", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npm run build && node dist/agent.js --test", "dev": "<PERSON>ra dev", "build": "tsc && mastra build", "build:ts": "tsc", "agent": "npm run build:ts && node dist/agent.js", "agent:test": "npm run build:ts && node dist/agent.js --test"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.1", "@mastra/libsql": "^0.10.0", "@mastra/mcp": "^0.10.1", "@mastra/memory": "^0.10.1", "dotenv": "^16.5.0", "openai": "^4.103.0", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.23", "mastra": "^0.10.1", "typescript": "^5.8.3"}}