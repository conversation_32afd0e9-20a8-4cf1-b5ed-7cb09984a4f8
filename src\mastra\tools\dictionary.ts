import { createTool } from '@mastra/core';
import { z } from 'zod';

export const getDefinitions = createTool({
  id: 'get_definitions',
  description: 'Get definitions for a word using the dictionary MCP server',
  inputSchema: z.object({
    word: z.string().describe('The word to get definitions for'),
  }),
  execute: async (context: any) => {
    console.log('🔧 Tool context:', context);
    const word = context.context?.word || context.word || context.input?.word || 'test';

    try {
      console.log(`🔍 Fallback dictionary tool çağrılıyor: get_definitions("${word}")`);
      console.log('⚠️ MCP tools kullanılamadı, mock data kullanılıyor...');

      // MCP server çalışmıyorsa mock data kullan
      console.log('📚 Mock sözlük verisi kullanılıyor...');

      const mockDefinitions: Record<string, string[]> = {
        'example': [
          'A thing characteristic of its kind or illustrating a general rule',
          'A person or thing regarded in terms of their fitness to be imitated or the likelihood of their being imitated',
          'An instance serving for illustration'
        ],
        'computer': [
          'An electronic device for storing and processing data, typically in binary form, according to instructions given to it in a variable program',
          'A person who makes calculations, especially with a calculating machine'
        ],
        'hello': [
          'Used as a greeting or to begin a phone conversation',
          'An exclamation of surprise'
        ],
        'test': [
          'A procedure intended to establish the quality, performance, or reliability of something',
          'An examination of knowledge or ability'
        ]
      };

      const definitions = mockDefinitions[word.toLowerCase()] || [
        `No definition found for "${word}". This word is not available in the dictionary.`
      ];

      console.log(`🎯 "${word}" için ${definitions.length} tanım bulundu (mock)`);

      return {
        success: true,
        word: word,
        definitions: definitions,
        message: `Definitions for "${word}" retrieved (mock data).`,
        source: 'Mock Dictionary (MCP server connection failed)'
      };
    } catch (error: any) {
      console.error('❌ MCP tool error:', error);
      return {
        error: 'There was an issue with the MCP connection. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error',
        word: word
      };
    }
  },
});
