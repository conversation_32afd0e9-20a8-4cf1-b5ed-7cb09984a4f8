import { createTool } from '@mastra/core';
import { z } from 'zod';

// MCP server URL'<PERSON> - <PERSON><PERSON><PERSON>an HTTP çağrısı için
const MCP_SERVER_URL = "https://server.smithery.ai/@ceydasimsekk/dictionarymcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79";

export const getDefinitions = createTool({
  id: 'get_definitions',
  description: 'Get definitions for a word using the dictionary MCP server',
  inputSchema: z.object({
    word: z.string().describe('The word to get definitions for'),
  }),
  execute: async (context: any) => {
    console.log('🔧 Tool context:', context);
    const word = context.context?.word || context.word || context.input?.word || 'test';

    // Önce Mastra'nın kendi MCP tool execution'ını deneyelim
    try {
      console.log('🔧 Trying Mastra MCP tool execution...');
      const mastraInstance = context.mastra;
      if (mastraInstance) {
        const mcpTools = await mastraInstance.getTools();
        console.log('🛠️ Mastra MCP tools:', Object.keys(mcpTools));

        if (mcpTools.smithery_get_definitions) {
          console.log('🔧 Calling via Mastra context...');
          const result = await mcpTools.smithery_get_definitions.execute({
            word,
            context: context.runtimeContext,
            mastra: mastraInstance
          });
          console.log('🎯 Mastra MCP result:', result);

          if (result && !result.isError) {
            let definitions = [];

            if (result.content && Array.isArray(result.content)) {
              definitions = result.content
                .filter((item: any) => item.type === 'text' && item.text)
                .map((item: any) => item.text);
            }

            return {
              success: true,
              word: word,
              definitions: definitions,
              data: result,
              message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
              source: 'Mastra MCP Context'
            };
          }
        }
      }
    } catch (mastraError) {
      console.log('⚠️ Mastra MCP execution failed:', mastraError);
    }

    try {
      console.log(`🔍 MCP tool çağrılıyor: get_definitions("${word}")`);

      // Önce MCP client ile gerçek server'ı deneyelim
      try {
        console.log('🌐 Gerçek MCP server bağlantısı deneniyor...');

        // Doğrudan HTTP çağrısı ile MCP server'ı deneyelim
        const response = await fetch(MCP_SERVER_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'tools/call',
            params: {
              name: 'get_definitions',
              arguments: { word: word }
            }
          })
        });

        console.log('🌐 HTTP response status:', response.status);

        if (response.ok) {
          const result = await response.json();
          console.log('🎯 HTTP MCP server yanıtı:', result);

          if (result.result && !result.error) {
            // Başarılı yanıt
            let definitions = [];

            if (result.result.content && Array.isArray(result.result.content)) {
              definitions = result.result.content
                .filter((item: any) => item.type === 'text' && item.text)
                .map((item: any) => item.text);
            } else if (typeof result.result === 'string') {
              definitions = [result.result];
            }

            return {
              success: true,
              word: word,
              definitions: definitions,
              data: result.result,
              message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
              source: 'Direct HTTP MCP Server'
            };
          } else {
            throw new Error(result.error?.message || 'MCP server error');
          }
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        console.log('🛠️ Available MCP tools:', Object.keys(tools));
        console.log('🔍 Tool structure:', JSON.stringify(tools, null, 2));

        // get_definitions tool'unu çağır - tool ismi smithery_get_definitions olarak görünüyor
        const toolName = 'smithery_get_definitions';
        if (tools[toolName]) {
          console.log(`🔧 Tool ${toolName} type:`, typeof tools[toolName]);
          console.log(`🔧 Tool ${toolName} input schema:`, tools[toolName].inputSchema);

          // Tool'u çağırmayı dene
          if (typeof tools[toolName] === 'function') {
            const result = await tools[toolName]({ word });
            console.log('🎯 MCP server yanıtı:', result);

            return {
              success: true,
              word: word,
              data: result,
              message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
              source: 'Real MCP Server'
            };
          } else if (tools[toolName].execute) {
            // Farklı parametre formatlarını dene
            console.log('🔧 Trying different parameter formats...');

            // Format 1: Direct object
            let finalResult: any = null;
            let successFormat = '';

            try {
              console.log('🔧 Format 1 - Direct object:', { word });
              const result1 = await tools[toolName].execute({ word });
              console.log('🎯 Format 1 result:', result1);
              if (result1 && !result1.isError) {
                finalResult = result1;
                successFormat = 'Format 1';
              }
            } catch (e: any) {
              console.log('❌ Format 1 failed:', e.message);
            }

            if (!finalResult) {
              try {
                console.log('🔧 Format 2 - Nested arguments:', { arguments: { word } });
                const result2 = await tools[toolName].execute({ arguments: { word } });
                console.log('🎯 Format 2 result:', result2);
                if (result2 && !result2.isError) {
                  finalResult = result2;
                  successFormat = 'Format 2';
                }
              } catch (e: any) {
                console.log('❌ Format 2 failed:', e.message);
              }
            }

            if (!finalResult) {
              try {
                console.log('🔧 Format 3 - String parameter:', word);
                const result3 = await tools[toolName].execute(word);
                console.log('🎯 Format 3 result:', result3);
                if (result3 && !result3.isError) {
                  finalResult = result3;
                  successFormat = 'Format 3';
                }
              } catch (e: any) {
                console.log('❌ Format 3 failed:', e.message);
              }
            }

            // Hiçbiri başarılı değilse son deneme
            if (!finalResult) {
              const result = await tools[toolName].execute({ word });
              console.log('🎯 Final MCP server yanıtı:', result);
              finalResult = result;
              successFormat = 'Final attempt';
            }

            console.log(`✅ Successful format: ${successFormat}`);

            // MCP server'dan gelen yanıtı işle
            if (finalResult && !finalResult.isError) {
              // Başarılı yanıt
              let definitions = [];

              if (finalResult.content && Array.isArray(finalResult.content)) {
                // Content array'inden text'leri çıkar
                definitions = finalResult.content
                  .filter((item: any) => item.type === 'text' && item.text)
                  .map((item: any) => item.text);
              } else if (typeof finalResult === 'string') {
                definitions = [finalResult];
              } else if (finalResult.text) {
                definitions = [finalResult.text];
              }

              return {
                success: true,
                word: word,
                definitions: definitions,
                data: finalResult,
                message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
                source: 'Real MCP Server'
              };
            } else {
              // Hata durumu
              const errorMessage = finalResult?.content?.[0]?.text || finalResult?.error || 'Bilinmeyen hata';
              console.log('❌ MCP server hatası:', errorMessage);
              throw new Error(`MCP server error: ${errorMessage}`);
            }
          } else {
            throw new Error(`Tool ${toolName} is not callable`);
          }
        } else {
          console.log('🔍 Available tools:', Object.keys(tools));
          throw new Error('get_definitions tool not found in MCP server');
        }

      } catch (mcpError) {
        console.log('⚠️ MCP server bağlantısı başarısız, mock data kullanılıyor:', mcpError);

        // MCP server çalışmıyorsa mock data kullan
        console.log('📚 Mock sözlük verisi kullanılıyor...');

        const mockDefinitions: Record<string, string[]> = {
          'example': [
            'Bir şeyi açıklamak veya göstermek için kullanılan örnek',
            'Bir durumu veya kavramı anlatmak için verilen misal',
            'Başkalarına model olacak davranış veya durum'
          ],
          'computer': [
            'Bilgisayar: Verileri işlemek, depolamak ve iletmek için kullanılan elektronik cihaz',
            'Hesaplama makinesi: Matematiksel işlemleri otomatik olarak yapan makine'
          ],
          'hello': [
            'Merhaba: Selamlama ifadesi',
            'Selam: Karşılaşma anında kullanılan nezaket ifadesi'
          ],
          'test': [
            'Test: Bir şeyin kalitesini, işlevselliğini veya performansını ölçmek için yapılan deneme',
            'Sınav: Bilgi veya beceri düzeyini ölçmek için yapılan değerlendirme'
          ]
        };

        const definitions = mockDefinitions[word.toLowerCase()] || [
          `"${word}" kelimesi için tanım bulunamadı. Bu kelime sözlükte mevcut değil.`
        ];

        console.log(`🎯 "${word}" için ${definitions.length} tanım bulundu (mock)`);

        return {
          success: true,
          word: word,
          definitions: definitions,
          message: `"${word}" kelimesinin tanımları alındı (mock data).`,
          source: 'Mock Dictionary (MCP server bağlantısı kurulamadı)'
        };
      }
    } catch (error) {
      console.error('❌ MCP tool error:', error);
      return {
        error: 'MCP bağlantısında sorun oluştu. Lütfen daha sonra tekrar deneyin.',
        details: error instanceof Error ? error.message : 'Unknown error',
        word: word
      };
    }
  },
});
