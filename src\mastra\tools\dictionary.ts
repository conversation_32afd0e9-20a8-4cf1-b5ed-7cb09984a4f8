import { createTool } from '@mastra/core';
import { MCPClient } from '@mastra/mcp';
import { z } from 'zod';

// MCP Client yapılandırması - gerçek MCP server bağlantısı için
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/dictionarymcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79"),
    },
  },
});

export const getDefinitions = createTool({
  id: 'get_definitions',
  description: 'Get definitions for a word using the dictionary MCP server',
  inputSchema: z.object({
    word: z.string().describe('The word to get definitions for'),
  }),
  execute: async (context: any) => {
    console.log('🔧 Tool context:', context);
    const word = context.context?.word || context.word || context.input?.word || 'test';

    try {
      console.log(`🔍 MCP tool çağrılıyor: get_definitions("${word}")`);

      // Önce MCP client ile gerçek server'ı deneyelim
      try {
        console.log('🌐 Gerçek MCP server bağlantısı deneniyor...');

        // MCP client'dan tools'ları al
        const tools = await mcp.getTools();
        console.log('🛠️ Available MCP tools:', Object.keys(tools));
        console.log('🔍 Tool structure:', JSON.stringify(tools, null, 2));

        // get_definitions tool'unu çağır - tool ismi smithery_get_definitions olarak görünüyor
        const toolName = 'smithery_get_definitions';
        if (tools[toolName]) {
          console.log(`🔧 Tool ${toolName} type:`, typeof tools[toolName]);
          console.log(`🔧 Tool ${toolName} structure:`, tools[toolName]);

          // Tool'u çağırmayı dene
          if (typeof tools[toolName] === 'function') {
            const result = await tools[toolName]({ word });
            console.log('🎯 MCP server yanıtı:', result);

            return {
              success: true,
              word: word,
              data: result,
              message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
              source: 'Real MCP Server'
            };
          } else if (tools[toolName].execute) {
            console.log('🔧 Calling MCP tool with parameters:', { word });
            const result = await tools[toolName].execute({ word });
            console.log('🎯 MCP server yanıtı:', result);

            return {
              success: true,
              word: word,
              data: result,
              message: `"${word}" kelimesinin tanımları MCP server'ından alındı.`,
              source: 'Real MCP Server'
            };
          } else {
            throw new Error(`Tool ${toolName} is not callable`);
          }
        } else {
          console.log('🔍 Available tools:', Object.keys(tools));
          throw new Error('get_definitions tool not found in MCP server');
        }

      } catch (mcpError) {
        console.log('⚠️ MCP server bağlantısı başarısız, mock data kullanılıyor:', mcpError);

        // MCP server çalışmıyorsa mock data kullan
        console.log('📚 Mock sözlük verisi kullanılıyor...');

        const mockDefinitions: Record<string, string[]> = {
          'example': [
            'Bir şeyi açıklamak veya göstermek için kullanılan örnek',
            'Bir durumu veya kavramı anlatmak için verilen misal',
            'Başkalarına model olacak davranış veya durum'
          ],
          'computer': [
            'Bilgisayar: Verileri işlemek, depolamak ve iletmek için kullanılan elektronik cihaz',
            'Hesaplama makinesi: Matematiksel işlemleri otomatik olarak yapan makine'
          ],
          'hello': [
            'Merhaba: Selamlama ifadesi',
            'Selam: Karşılaşma anında kullanılan nezaket ifadesi'
          ],
          'test': [
            'Test: Bir şeyin kalitesini, işlevselliğini veya performansını ölçmek için yapılan deneme',
            'Sınav: Bilgi veya beceri düzeyini ölçmek için yapılan değerlendirme'
          ]
        };

        const definitions = mockDefinitions[word.toLowerCase()] || [
          `"${word}" kelimesi için tanım bulunamadı. Bu kelime sözlükte mevcut değil.`
        ];

        console.log(`🎯 "${word}" için ${definitions.length} tanım bulundu (mock)`);

        return {
          success: true,
          word: word,
          definitions: definitions,
          message: `"${word}" kelimesinin tanımları alındı (mock data).`,
          source: 'Mock Dictionary (MCP server bağlantısı kurulamadı)'
        };
      }
    } catch (error) {
      console.error('❌ MCP tool error:', error);
      return {
        error: 'MCP bağlantısında sorun oluştu. Lütfen daha sonra tekrar deneyin.',
        details: error instanceof Error ? error.message : 'Unknown error',
        word: word
      };
    }
  },
});
