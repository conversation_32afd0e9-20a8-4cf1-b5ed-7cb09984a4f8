import { createTool } from '@mastra/core';
import { z } from 'zod';

export const getDefinitions = createTool({
  id: 'get_definitions',
  description: 'Get definitions for a word using the dictionary MCP server',
  inputSchema: z.object({
    word: z.string().describe('The word to get definitions for'),
  }),
  execute: async (context: any) => {
    console.log('🔧 Tool context:', context);
    const word = context.context?.word || context.word || context.input?.word || 'test';

    try {
      console.log(`🔍 Fallback dictionary tool çağrılıyor: get_definitions("${word}")`);
      console.log('⚠️ MCP tools kullanılamadı, mock data kullanılıyor...');

      // MCP server çalışmıyorsa mock data kullan
      console.log('📚 Mock sözlük verisi kullanılıyor...');

      const mockDefinitions: Record<string, string[]> = {
        'example': [
          'Bir şeyi açıklamak veya göstermek için kull<PERSON>lan <PERSON>k',
          '<PERSON>ir durumu veya kavramı anlatmak için verilen misal',
          'Başkalarına model olacak davranış veya durum'
        ],
        'computer': [
          'Bilgisayar: Verileri işlemek, depolamak ve iletmek için kullanılan elektronik cihaz',
          'Hesaplama makinesi: Matematiksel işlemleri otomatik olarak yapan makine'
        ],
        'hello': [
          'Merhaba: Selamlama ifadesi',
          'Selam: Karşılaşma anında kullanılan nezaket ifadesi'
        ],
        'test': [
          'Test: Bir şeyin kalitesini, işlevselliğini veya performansını ölçmek için yapılan deneme',
          'Sınav: Bilgi veya beceri düzeyini ölçmek için yapılan değerlendirme'
        ]
      };

      const definitions = mockDefinitions[word.toLowerCase()] || [
        `"${word}" kelimesi için tanım bulunamadı. Bu kelime sözlükte mevcut değil.`
      ];

      console.log(`🎯 "${word}" için ${definitions.length} tanım bulundu (mock)`);

      return {
        success: true,
        word: word,
        definitions: definitions,
        message: `"${word}" kelimesinin tanımları alındı (mock data).`,
        source: 'Mock Dictionary (MCP server bağlantısı kurulamadı)'
      };
    } catch (error: any) {
      console.error('❌ MCP tool error:', error);
      return {
        error: 'MCP bağlantısında sorun oluştu. Lütfen daha sonra tekrar deneyin.',
        details: error instanceof Error ? error.message : 'Unknown error',
        word: word
      };
    }
  },
});
