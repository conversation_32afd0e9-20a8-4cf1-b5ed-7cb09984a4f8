import { mastra } from "./src/mastra/index.js";
import dotenv from "dotenv";
import readline from "readline";

dotenv.config();

// Test fonksiyonu
async function testAgent() {
  try {
    console.log("🤖 Agent başlatılıyor...");

    const agent = mastra.getAgent('dictionaryAgent');
    const response = await agent.generate([
      {
        role: 'user',
        content: 'Define the word "example" using the smithery_get_definitions MCP tool. You must use the tool, do not provide definitions from your own knowledge.',
      }
    ]);

    console.log("📝 Agent yanıtı:", response.text);
  } catch (error) {
    console.error("❌ Hata:", error);
  }
}

// Etkileşimli chat fonksiyonu
async function startChat() {
  console.log("🚀 Mastra Agent Chat başlatıldı!");
  console.log("💬 Bir kelime yazın ve anlamını öğrenin (çıkmak için 'exit' yazın)");

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const askQuestion = () => {
    rl.question('\n🔍 Hangi kelimenin anlamını öğrenmek istiyorsunuz? ', async (input: string) => {
      if (input.toLowerCase() === 'exit') {
        console.log("👋 Görüşürüz!");
        rl.close();
        return;
      }

      try {
        console.log("🤔 Düşünüyorum...");
        const agent = mastra.getAgent('dictionaryAgent');
        const response = await agent.generate([
          {
            role: 'user',
            content: `Define the word "${input}" using the smithery_get_definitions MCP tool. You must use the tool, do not provide definitions from your own knowledge. Respond in English.`,
          }
        ]);

        console.log("🤖 Agent:", response.text);
      } catch (error) {
        console.error("❌ Hata:", error instanceof Error ? error.message : error);
      }

      askQuestion();
    });
  };

  askQuestion();
}

// Ana fonksiyon
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--test')) {
    await testAgent();
  } else {
    await startChat();
  }
}

// Programı başlat
main().catch(console.error);

export { mastra };
