import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import OpenAI from "openai";
import dotenv from "dotenv";

dotenv.config();

const mcp = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@ceydasimsekk/dictionarymcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79",
    },
  },
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const agent = new Agent({
  client: openai,
  mcpClient: mcp,
  name: "DictionaryAgent",
  model: "gpt-4o-mini",
});

async function test() {
  const response = await agent.invoke({
    prompt: 'Define the word "example" using the MCP tool "get_definitions".',
  });
  console.log(response);
}

test();
