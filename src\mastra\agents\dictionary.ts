import { Agent } from '@mastra/core';
import { MCPClient } from '@mastra/mcp';
import { openai } from '@ai-sdk/openai';

// MCP Client yapılandırması
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/dictionarymcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79"),
    },
  },
});

// OpenAI model yapılandırması
const model = openai('gpt-4o-mini');

export const dictionaryAgent = new Agent({
  name: 'dictionaryAgent',
  instructions: 'Sen bir sözlük asistanısın. Kullanıcıların sorduğu kelimelerin anlamlarını MCP araçlarını kullanarak bulup açıklıyorsun. Türkçe yanıt ver.',
  model,
});
