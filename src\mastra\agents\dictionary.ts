import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { getDefinitions } from '../tools/dictionary.js';

// OpenAI model yapılandırması
const model = openai('gpt-4o-mini');

export const dictionaryAgent = new Agent({
  name: 'dictionaryAgent',
  instructions: `Sen bir sözlük asistanısın. SADECE MCP araçlarını kullanarak kelimelerin anlamlarını bulabilirsin.

  ÖNEMLİ KURALLAR:
  1. Bir kelime sorulduğunda MUTLAKA "get_definitions" tool'unu kullan
  2. <PERSON>di bilginden kelime anlamı verme, sadece tool'dan gelen veriyi kullan
  3. Eğer tool çalışmazsa, kullanıcıya MCP bağlantısında sorun olduğunu söyle
  4. Yanıtlarını Türkçe ver

  Kullanılabilir Tools:
  - get_definitions: <PERSON><PERSON> keli<PERSON>in tanımlarını getirir`,
  model,
  tools: {
    getDefinitions,
  },
});
