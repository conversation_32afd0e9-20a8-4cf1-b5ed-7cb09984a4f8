import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { mcp } from '../mcp.js';
import { getDefinitions } from '../tools/dictionary.js';

// OpenAI model yapılandırması
const model = openai('gpt-4o-mini');

// MCP tools'ları al ve agent'ı oluştur
async function createDictionaryAgent() {
  try {
    console.log('🔧 MCP tools alınıyor...');
    const mcpTools = await mcp.getTools();
    console.log('🛠️ MCP tools:', Object.keys(mcpTools));

    return new Agent({
      name: 'dictionaryAgent',
      instructions: `Sen bir sözlük asistanısın. MCP araçlarını kullanarak kelimelerin anlamlarını bulabilirsin.

      ÖNEMLİ KURALLAR:
      1. Bir kelime sorulduğunda ÖNCE "smithery_get_definitions" tool'unu kullanmayı dene
      2. <PERSON><PERSON><PERSON> smithery_get_definitions çalışmazsa, "get_definitions" tool'unu kullan
      3. <PERSON><PERSON> bilginden kelime anlamı verme, sadece tool'dan gelen veriyi kullan
      4. Tool'dan gelen tanımları açık ve anlaşılır şekilde listele

      Kullanılabilir Tools:
      - smithery_get_definitions: Gerçek MCP server'dan kelime tanımları (öncelikli)
      - get_definitions: Fallback sözlük tool'u
      - Diğer MCP tools: ${Object.keys(mcpTools).join(', ')}`,
      model,
      tools: {
        getDefinitions,
        ...mcpTools, // MCP tools'ları ekle
      },
    });
  } catch (error) {
    console.log('⚠️ MCP tools alınamadı, fallback agent oluşturuluyor:', error);

    return new Agent({
      name: 'dictionaryAgent',
      instructions: `Sen bir sözlük asistanısın. get_definitions tool'unu kullanarak kelimelerin anlamlarını bulabilirsin.

      ÖNEMLİ KURALLAR:
      1. Bir kelime sorulduğunda MUTLAKA "get_definitions" tool'unu kullan
      2. Kendi bilginden kelime anlamı verme, sadece tool'dan gelen veriyi kullan
      3. Eğer tool çalışmazsa, kullanıcıya MCP bağlantısında sorun olduğunu söyle
      4. Yanıtlarını Türkçe ver

      Kullanılabilir Tools:
      - get_definitions: Bir kelimenin tanımlarını getirir`,
      model,
      tools: {
        getDefinitions,
      },
    });
  }
}

export const dictionaryAgent = await createDictionaryAgent();
